import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/constants.dart';

/// Enhanced transport controls with temporary editing support
class EnhancedTransportControls extends StatefulWidget {
  final bool isPlaying;
  final double currentPosition;
  final double duration;
  final VoidCallback onPlayPause;
  final VoidCallback? onStop;
  final VoidCallback? onRestart;
  final VoidCallback onSeekBackward;
  final VoidCallback onSeekForward;
  final VoidCallback onSkipToStart;
  final VoidCallback onSkipToEnd;

  // Enhanced trim/cut functionality
  final bool isTrimMode;
  final bool isCutMode;
  final bool isPreviewMode;
  final VoidCallback onToggleTrimMode;
  final VoidCallback onToggleCutMode;
  final VoidCallback? onApplyTemporaryTrim;
  final VoidCallback? onApplyTemporaryCut;
  final VoidCallback? onResetTemporaryEdits;
  final VoidCallback? onPreviewTemporaryEdits;
  final VoidCallback? onCommitTemporaryEdits;
  final String? currentVideoPath;

  const EnhancedTransportControls({
    super.key,
    required this.isPlaying,
    required this.currentPosition,
    required this.duration,
    required this.onPlayPause,
    this.onStop,
    this.onRestart,
    required this.onSeekBackward,
    required this.onSeekForward,
    required this.onSkipToStart,
    required this.onSkipToEnd,
    required this.isTrimMode,
    required this.isCutMode,
    this.isPreviewMode = false,
    required this.onToggleTrimMode,
    required this.onToggleCutMode,
    this.onApplyTemporaryTrim,
    this.onApplyTemporaryCut,
    this.onResetTemporaryEdits,
    this.onPreviewTemporaryEdits,
    this.onCommitTemporaryEdits,
    this.currentVideoPath,
  });

  @override
  State<EnhancedTransportControls> createState() =>
      _EnhancedTransportControlsState();
}

class _EnhancedTransportControlsState extends State<EnhancedTransportControls>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  bool _showAdvancedControls = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // Start pulse animation for active modes
    if (widget.isTrimMode || widget.isCutMode || widget.isPreviewMode) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(EnhancedTransportControls oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Handle pulse animation based on active modes
    final shouldPulse =
        widget.isTrimMode || widget.isCutMode || widget.isPreviewMode;
    final wasPulsing =
        oldWidget.isTrimMode || oldWidget.isCutMode || oldWidget.isPreviewMode;

    if (shouldPulse && !wasPulsing) {
      _pulseController.repeat(reverse: true);
    } else if (!shouldPulse && wasPulsing) {
      _pulseController.stop();
      _pulseController.reset();
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 800;

    return Container(
      height: isSmallScreen ? 120 : 80, // Increase height for small screens
      decoration: BoxDecoration(
        color: Color(AppConstants.surfaceColor),
        border: Border(
          top: BorderSide(color: Colors.white.withValues(alpha: 0.1)),
          bottom: BorderSide(color: Colors.white.withValues(alpha: 0.1)),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        child: Column(
          children: [
            // Main controls - responsive layout
            Expanded(
              child: isSmallScreen ? _buildCompactLayout() : _buildFullLayout(),
            ),

            // Advanced controls (collapsible)
            if (_showAdvancedControls) _buildAdvancedControls(),
          ],
        ),
      ),
    );
  }

  Widget _buildFullLayout() {
    return Row(
      children: [
        // Playback controls
        _buildPlaybackControls(),

        const SizedBox(width: 16),

        // Position display
        _buildPositionDisplay(),

        const Spacer(),

        // Edit mode controls
        _buildEditModeControls(),

        const SizedBox(width: 8),

        // Advanced controls toggle
        _buildAdvancedToggle(),
      ],
    );
  }

  Widget _buildCompactLayout() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isVerySmall = screenWidth < 400;

    return Column(
      children: [
        // First row: Playback controls and position
        Expanded(
          child: isVerySmall
              ? SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Row(
                    children: [
                      _buildCompactPlaybackControls(),
                      const SizedBox(width: 16),
                      _buildPositionDisplay(),
                      const SizedBox(width: 8),
                      _buildAdvancedToggle(),
                    ],
                  ),
                )
              : Row(
                  children: [
                    Flexible(
                      flex: 3,
                      child: _buildCompactPlaybackControls(),
                    ),
                    const SizedBox(width: 8),
                    Flexible(
                      flex: 2,
                      child: _buildPositionDisplay(),
                    ),
                    const SizedBox(width: 8),
                    _buildAdvancedToggle(),
                  ],
                ),
        ),

        const SizedBox(height: 4),

        // Second row: Edit mode controls
        Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildEditModeControls(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPlaybackControls() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Skip to start
        _buildControlButton(
          icon: Icons.skip_previous,
          onPressed: widget.onSkipToStart,
          tooltip: 'Skip to Start',
        ),

        const SizedBox(width: 8),

        // Seek backward
        _buildControlButton(
          icon: Icons.replay_10,
          onPressed: widget.onSeekBackward,
          tooltip: 'Seek Backward (10s)',
        ),

        const SizedBox(width: 12),

        // Play/Pause (larger button)
        _buildPlayPauseButton(),

        const SizedBox(width: 12),

        // Seek forward
        _buildControlButton(
          icon: Icons.forward_10,
          onPressed: widget.onSeekForward,
          tooltip: 'Seek Forward (10s)',
        ),

        const SizedBox(width: 8),

        // Skip to end
        _buildControlButton(
          icon: Icons.skip_next,
          onPressed: widget.onSkipToEnd,
          tooltip: 'Skip to End',
        ),
      ],
    );
  }

  Widget _buildCompactPlaybackControls() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Seek backward
        _buildControlButton(
          icon: Icons.replay_10,
          onPressed: widget.onSeekBackward,
          tooltip: 'Seek Backward (10s)',
        ),

        const SizedBox(width: 6),

        // Play/Pause (smaller button for compact layout)
        _buildCompactPlayPauseButton(),

        const SizedBox(width: 6),

        // Seek forward
        _buildControlButton(
          icon: Icons.forward_10,
          onPressed: widget.onSeekForward,
          tooltip: 'Seek Forward (10s)',
        ),
      ],
    );
  }

  Widget _buildPlayPauseButton() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: widget.isPreviewMode ? _pulseAnimation.value : 1.0,
          child: Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: widget.isPreviewMode
                  ? Colors.orange
                  : Color(AppConstants.accentColor),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: (widget.isPreviewMode
                          ? Colors.orange
                          : Color(AppConstants.accentColor))
                      .withValues(alpha: 0.3),
                  blurRadius: 8,
                  spreadRadius: 2,
                ),
              ],
            ),
            child: IconButton(
              onPressed: widget.onPlayPause,
              icon: Icon(
                widget.isPlaying ? Icons.pause : Icons.play_arrow,
                color: Colors.white,
                size: 24,
              ),
              tooltip: widget.isPlaying ? 'Pause' : 'Play',
            ),
          ),
        );
      },
    );
  }

  Widget _buildCompactPlayPauseButton() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: widget.isPreviewMode ? _pulseAnimation.value : 1.0,
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: widget.isPreviewMode
                  ? Colors.orange
                  : Color(AppConstants.accentColor),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: (widget.isPreviewMode
                          ? Colors.orange
                          : Color(AppConstants.accentColor))
                      .withValues(alpha: 0.3),
                  blurRadius: 6,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: IconButton(
              onPressed: widget.onPlayPause,
              icon: Icon(
                widget.isPlaying ? Icons.pause : Icons.play_arrow,
                color: Colors.white,
                size: 20,
              ),
              tooltip: widget.isPlaying ? 'Pause' : 'Play',
            ),
          ),
        );
      },
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    required String tooltip,
    Color? color,
  }) {
    return Tooltip(
      message: tooltip,
      child: IconButton(
        onPressed: onPressed,
        icon: Icon(
          icon,
          color: color ?? Colors.white,
          size: 20,
        ),
      ),
    );
  }

  Widget _buildPositionDisplay() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Color(AppConstants.backgroundColor),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            _formatTime(widget.currentPosition),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.bold,
              fontFeatures: [FontFeature.tabularFigures()],
            ),
          ),
          Text(
            ' / ',
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.5),
              fontSize: 14,
            ),
          ),
          Text(
            _formatTime(widget.duration),
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.7),
              fontSize: 14,
              fontFeatures: const [FontFeature.tabularFigures()],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditModeControls() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isVerySmall = screenWidth < 600;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Trim mode button
        _buildModeButton(
          icon: Icons.content_cut,
          label: isVerySmall ? '' : 'Trim',
          isActive: widget.isTrimMode,
          onPressed: widget.onToggleTrimMode,
          tooltip: 'Trim Mode - Select start and end points',
          activeColor: Colors.blue,
          compact: isVerySmall,
        ),

        SizedBox(width: isVerySmall ? 8 : 12),

        // Cut mode button
        _buildModeButton(
          icon: Icons.call_split,
          label: isVerySmall ? '' : 'Cut',
          isActive: widget.isCutMode,
          onPressed: widget.onToggleCutMode,
          tooltip: 'Cut Mode - Remove sections',
          activeColor: Colors.red,
          compact: isVerySmall,
        ),

        SizedBox(width: isVerySmall ? 8 : 16),

        // Preview mode button
        if (widget.onPreviewTemporaryEdits != null)
          _buildModeButton(
            icon: Icons.preview,
            label: isVerySmall ? '' : 'Preview',
            isActive: widget.isPreviewMode,
            onPressed: widget.onPreviewTemporaryEdits!,
            tooltip: 'Preview temporary edits',
            activeColor: Colors.orange,
            compact: isVerySmall,
          ),
      ],
    );
  }

  Widget _buildModeButton({
    required IconData icon,
    required String label,
    required bool isActive,
    required VoidCallback onPressed,
    required String tooltip,
    Color activeColor = Colors.blue,
    bool compact = false,
  }) {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: isActive ? _pulseAnimation.value : 1.0,
          child: Tooltip(
            message: tooltip,
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: onPressed,
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  padding: compact
                      ? const EdgeInsets.all(8)
                      : const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: isActive
                        ? activeColor.withValues(alpha: 0.2)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isActive
                          ? activeColor
                          : Colors.white.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: compact
                      ? Icon(
                          icon,
                          color: isActive ? activeColor : Colors.white,
                          size: 16,
                        )
                      : Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              icon,
                              color: isActive ? activeColor : Colors.white,
                              size: 16,
                            ),
                            if (label.isNotEmpty) ...[
                              const SizedBox(width: 6),
                              Text(
                                label,
                                style: TextStyle(
                                  color: isActive ? activeColor : Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ],
                        ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAdvancedToggle() {
    return IconButton(
      onPressed: () {
        setState(() {
          _showAdvancedControls = !_showAdvancedControls;
        });
        HapticFeedback.lightImpact();
      },
      icon: AnimatedRotation(
        turns: _showAdvancedControls ? 0.5 : 0.0,
        duration: const Duration(milliseconds: 200),
        child: const Icon(
          Icons.expand_more,
          color: Colors.white,
          size: 20,
        ),
      ),
      tooltip: _showAdvancedControls ? 'Hide Advanced' : 'Show Advanced',
    );
  }

  Widget _buildAdvancedControls() {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      height: 40,
      child: isSmallScreen
          ? _buildCompactAdvancedControls()
          : _buildFullAdvancedControls(),
    );
  }

  Widget _buildFullAdvancedControls() {
    return Row(
      children: [
        // Apply button (for trim/cut)
        if ((widget.isTrimMode && widget.onApplyTemporaryTrim != null) ||
            (widget.isCutMode && widget.onApplyTemporaryCut != null))
          _buildActionButton(
            icon: Icons.check,
            label: 'Apply',
            onPressed: widget.isTrimMode
                ? widget.onApplyTemporaryTrim!
                : widget.onApplyTemporaryCut!,
            tooltip: 'Apply ${widget.isTrimMode ? 'Trim' : 'Cut'}',
            color: Colors.green,
          ),

        const SizedBox(width: 12),

        // Reset button
        if (widget.onResetTemporaryEdits != null)
          _buildActionButton(
            icon: Icons.restore,
            label: 'Reset',
            onPressed: widget.onResetTemporaryEdits!,
            tooltip: 'Reset all temporary edits',
            color: Colors.orange,
          ),

        const SizedBox(width: 12),

        // Commit button (make permanent)
        if (widget.onCommitTemporaryEdits != null)
          _buildActionButton(
            icon: Icons.save,
            label: 'Commit',
            onPressed: widget.onCommitTemporaryEdits!,
            tooltip: 'Make edits permanent',
            color: Colors.purple,
          ),

        const Spacer(),

        // Stop and restart buttons
        if (widget.onStop != null)
          _buildControlButton(
            icon: Icons.stop,
            onPressed: widget.onStop!,
            tooltip: 'Stop',
            color: Colors.red,
          ),

        if (widget.onRestart != null) ...[
          const SizedBox(width: 8),
          _buildControlButton(
            icon: Icons.restart_alt,
            onPressed: widget.onRestart!,
            tooltip: 'Restart',
            color: Colors.blue,
          ),
        ],
      ],
    );
  }

  Widget _buildCompactAdvancedControls() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          // Apply button (for trim/cut)
          if ((widget.isTrimMode && widget.onApplyTemporaryTrim != null) ||
              (widget.isCutMode && widget.onApplyTemporaryCut != null))
            _buildActionButton(
              icon: Icons.check,
              label: '',
              onPressed: widget.isTrimMode
                  ? widget.onApplyTemporaryTrim!
                  : widget.onApplyTemporaryCut!,
              tooltip: 'Apply ${widget.isTrimMode ? 'Trim' : 'Cut'}',
              color: Colors.green,
            ),

          const SizedBox(width: 8),

          // Reset button
          if (widget.onResetTemporaryEdits != null)
            _buildActionButton(
              icon: Icons.restore,
              label: '',
              onPressed: widget.onResetTemporaryEdits!,
              tooltip: 'Reset all temporary edits',
              color: Colors.orange,
            ),

          const SizedBox(width: 8),

          // Commit button (make permanent)
          if (widget.onCommitTemporaryEdits != null)
            _buildActionButton(
              icon: Icons.save,
              label: '',
              onPressed: widget.onCommitTemporaryEdits!,
              tooltip: 'Make edits permanent',
              color: Colors.purple,
            ),

          const SizedBox(width: 16),

          // Stop and restart buttons
          if (widget.onStop != null)
            _buildControlButton(
              icon: Icons.stop,
              onPressed: widget.onStop!,
              tooltip: 'Stop',
              color: Colors.red,
            ),

          if (widget.onRestart != null) ...[
            const SizedBox(width: 8),
            _buildControlButton(
              icon: Icons.restart_alt,
              onPressed: widget.onRestart!,
              tooltip: 'Restart',
              color: Colors.blue,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    required String tooltip,
    required Color color,
  }) {
    return Tooltip(
      message: tooltip,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(6),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(color: color, width: 1),
            ),
            child: label.isEmpty
                ? Icon(icon, color: color, size: 14)
                : Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(icon, color: color, size: 14),
                      const SizedBox(width: 4),
                      Text(
                        label,
                        style: TextStyle(
                          color: color,
                          fontSize: 11,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }

  String _formatTime(double seconds) {
    final minutes = (seconds / 60).floor();
    final remainingSeconds = (seconds % 60).floor();
    final milliseconds = ((seconds % 1) * 100).floor();

    return '${minutes.toString().padLeft(2, '0')}:'
        '${remainingSeconds.toString().padLeft(2, '0')}.'
        '${milliseconds.toString().padLeft(2, '0')}';
  }
}
